{"_comment": "This file is generated. Please edit .homeycompose/app.json instead.", "id": "com.ruijie.x32pro", "version": "1.0.0", "compatibility": ">=12.3.0", "sdk": 3, "platforms": ["local"], "name": {"en": "Ruijie X32-PRO"}, "description": {"en": "Control your Ruijie X32-PRO WiFi router with Homey"}, "category": ["internet"], "permissions": ["homey:manager:api"], "images": {"small": "/assets/images/small.png", "large": "/assets/images/large.png", "xlarge": "/assets/images/xlarge.png"}, "author": {"name": "Your Name", "email": "<EMAIL>"}, "contributing": {"donate": {"paypal": {"username": "YourPayPalUsername"}}}, "flow": {"triggers": [{"id": "connected_devices_changed", "title": {"en": "Connected devices changed"}, "hint": {"en": "Triggered when the list of connected devices changes."}, "args": [{"name": "device", "type": "device", "filter": "driver_id=router"}], "tokens": [{"name": "count", "type": "number", "title": {"en": "Count"}, "example": 5}, {"name": "devices", "type": "string", "title": {"en": "Devices"}}]}], "conditions": [{"id": "device_connected", "title": {"en": "A device !{{is|is not}} connected"}, "hint": {"en": "Checks if a device with the specified MAC address is connected to the router."}, "args": [{"name": "device", "type": "device", "filter": "driver_id=router"}, {"name": "mac_address", "type": "text", "placeholder": {"en": "AA:BB:CC:DD:EE:FF"}}]}], "actions": [{"id": "restart_router", "title": {"en": "Restart router"}, "hint": {"en": "Restarts the Ruijie X32-PRO router."}, "args": [{"name": "device", "type": "device", "filter": "driver_id=router"}]}, {"id": "toggle_guest_wifi", "title": {"en": "Turn guest WiFi !{{on|off}}"}, "hint": {"en": "Enables or disables the guest WiFi network."}, "args": [{"name": "device", "type": "device", "filter": "driver_id=router"}, {"name": "enabled", "type": "dropdown", "values": [{"id": "true", "label": {"en": "On"}}, {"id": "false", "label": {"en": "Off"}}]}]}]}, "drivers": [{"name": {"en": "router"}, "class": "networkrouter", "capabilities": ["measure_data_rate", "measure_data_size", "measure_signal_strength"], "platforms": ["local"], "connectivity": [], "images": {"small": "/drivers/router/assets/images/small.png", "large": "/drivers/router/assets/images/large.png", "xlarge": "/drivers/router/assets/images/xlarge.png"}, "id": "router"}], "capabilities": {"guestwifi": {"type": "boolean", "title": {"en": "Guest <PERSON><PERSON><PERSON><PERSON>"}, "getable": true, "setable": true, "uiComponent": "toggle", "icon": "/assets/capabilities/guestwifi.svg"}}}