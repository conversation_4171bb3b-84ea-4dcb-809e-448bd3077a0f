#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to explore the Ruijie X32-PRO router's LuCI interface
 * This helps us find the correct URLs for status, wireless, and other pages
 */

const axios = require('axios');

// Configuration
const ROUTER_CONFIG = {
  ip: '*************',
  password: 'admin'
};

async function exploreRouterInterface() {
  console.log('=== Ruijie X32-PRO Router Interface Explorer ===\n');
  
  // Create axios instance
  const client = axios.create({
    baseURL: `http://${ROUTER_CONFIG.ip}`,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    },
    withCredentials: true,
  });

  try {
    // Step 1: Login
    console.log('Step 1: Logging in...');
    const loginPageResponse = await client.get('/cgi-bin/luci/');
    
    const formData = new URLSearchParams();
    formData.append('loginPass', ROUTER_CONFIG.password);
    
    await client.post('/cgi-bin/luci/', formData);
    console.log('✅ Logged in successfully\n');

    // Step 2: Get the main page after login to see available menu items
    console.log('Step 2: Exploring main interface...');
    const mainPageResponse = await client.get('/cgi-bin/luci/');
    
    // Save the main page for analysis
    const fs = require('fs');
    fs.writeFileSync('router-main-page.html', mainPageResponse.data);
    console.log('✅ Main page saved to router-main-page.html\n');

    // Step 3: Try common LuCI paths
    console.log('Step 3: Testing common LuCI paths...');
    const commonPaths = [
      '/cgi-bin/luci/admin',
      '/cgi-bin/luci/admin/status',
      '/cgi-bin/luci/admin/system',
      '/cgi-bin/luci/admin/network',
      '/cgi-bin/luci/admin/network/wireless',
      '/cgi-bin/luci/admin/services',
      '/cgi-bin/luci/admin/status/overview',
      '/cgi-bin/luci/admin/status/routes',
      '/cgi-bin/luci/admin/status/iptables',
      '/cgi-bin/luci/admin/status/processes',
      '/cgi-bin/luci/admin/status/realtime',
      '/cgi-bin/luci/admin/status/load',
      '/cgi-bin/luci/admin/status/bandwidth',
      '/cgi-bin/luci/admin/status/wireless',
      '/cgi-bin/luci/admin/status/connections',
      '/cgi-bin/luci/admin/network/dhcp',
      '/cgi-bin/luci/admin/network/hosts',
      '/cgi-bin/luci/admin/network/routes',
      '/cgi-bin/luci/admin/network/firewall',
      '/cgi-bin/luci/admin/system/admin',
      '/cgi-bin/luci/admin/system/system',
      '/cgi-bin/luci/admin/system/packages',
      '/cgi-bin/luci/admin/system/startup',
      '/cgi-bin/luci/admin/system/crontab',
      '/cgi-bin/luci/admin/system/mounts',
      '/cgi-bin/luci/admin/system/leds',
      '/cgi-bin/luci/admin/system/flashops',
      '/cgi-bin/luci/admin/system/reboot'
    ];

    const workingPaths = [];
    const failedPaths = [];

    for (const path of commonPaths) {
      try {
        const response = await client.get(path);
        if (response.status === 200) {
          workingPaths.push({
            path: path,
            title: extractTitle(response.data),
            hasForm: response.data.includes('<form'),
            hasTable: response.data.includes('<table'),
            size: response.data.length
          });
          console.log(`✅ ${path} - ${extractTitle(response.data)}`);
        }
      } catch (error) {
        if (error.response && error.response.status === 404) {
          failedPaths.push(path);
          console.log(`❌ ${path} - 404 Not Found`);
        } else {
          console.log(`⚠️  ${path} - ${error.message}`);
        }
      }
      
      // Small delay to avoid overwhelming the router
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('\n=== SUMMARY ===');
    console.log(`Working paths: ${workingPaths.length}`);
    console.log(`Failed paths: ${failedPaths.length}\n`);

    if (workingPaths.length > 0) {
      console.log('📋 Working paths with details:');
      workingPaths.forEach(item => {
        console.log(`  ${item.path}`);
        console.log(`    Title: ${item.title}`);
        console.log(`    Has forms: ${item.hasForm}`);
        console.log(`    Has tables: ${item.hasTable}`);
        console.log(`    Size: ${item.size} bytes`);
        console.log('');
      });

      // Save detailed info about promising paths
      const promisingPaths = workingPaths.filter(item => 
        item.path.includes('status') || 
        item.path.includes('wireless') || 
        item.path.includes('network') ||
        item.path.includes('system')
      );

      console.log('💾 Saving content of promising paths...');
      for (const item of promisingPaths) {
        try {
          const response = await client.get(item.path);
          const filename = `router-page-${item.path.replace(/[^a-zA-Z0-9]/g, '_')}.html`;
          fs.writeFileSync(filename, response.data);
          console.log(`  Saved ${item.path} to ${filename}`);
        } catch (error) {
          console.log(`  Failed to save ${item.path}: ${error.message}`);
        }
      }
    }

    console.log('\n🎉 Exploration completed!');
    console.log('Check the saved HTML files to understand the router interface structure.');

  } catch (error) {
    console.error('❌ Exploration failed:', error.message);
  }
}

function extractTitle(html) {
  const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
  if (titleMatch && titleMatch[1]) {
    return titleMatch[1].trim();
  }
  
  // Try to find h1, h2, or other heading tags
  const headingMatch = html.match(/<h[1-6][^>]*>([^<]+)<\/h[1-6]>/i);
  if (headingMatch && headingMatch[1]) {
    return headingMatch[1].trim();
  }
  
  return 'Unknown';
}

// Run the exploration
exploreRouterInterface().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
