"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("source-map-support/register");
const homey_1 = __importDefault(require("homey"));
class RuijieX32ProApp extends homey_1.default.App {
    /**
     * onInit is called when the app is initialized.
     */
    async onInit() {
        this.log('Ruijie X32-PRO app has been initialized');
        // Register flow cards for router control
        this.registerFlowCards();
    }
    /**
     * Register flow cards for router control
     */
    registerFlowCards() {
        // Action flow card for restarting the router
        const restartRouterAction = this.homey.flow.getActionCard('restart_router');
        restartRouterAction.registerRunListener(async (args, state) => {
            const { device } = args;
            return device.restart();
        });
        // Action flow card for enabling/disabling guest WiFi
        const toggleGuestWifiAction = this.homey.flow.getActionCard('toggle_guest_wifi');
        toggleGuestWifiAction.registerRunListener(async (args, state) => {
            const { device, enabled } = args;
            return device.setGuestWifi(enabled);
        });
        // Condition flow card for checking if a device is connected
        const deviceConnectedCondition = this.homey.flow.getConditionCard('device_connected');
        deviceConnectedCondition.registerRunListener(async (args, state) => {
            const { device, mac_address } = args;
            return device.isDeviceConnected(mac_address);
        });
    }
}
module.exports = RuijieX32ProApp;
//# sourceMappingURL=app.js.map