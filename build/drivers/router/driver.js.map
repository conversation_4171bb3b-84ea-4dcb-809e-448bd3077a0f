{"version": 3, "file": "driver.js", "sourceRoot": "", "sources": ["../../../src/drivers/router/driver.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAG1B,MAAM,YAAa,SAAQ,eAAK,CAAC,MAAM;IAAvC;;QACU,kBAAa,GAAQ,IAAI,CAAC;IAwJpC,CAAC;IAtJC;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAEtC,MAAM,gBAAgB,GAAG;YACvB,EAAE,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,uCAAuC,EAAE;YACtE,EAAE,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,qCAAqC,EAAE;YAClE,EAAE,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,qCAAqC,EAAE;SACnE,CAAC;QAEF,MAAM,iBAAiB,GAAG,EAAE,CAAC;QAE7B,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE;YACrC,IAAI;gBACF,IAAI,CAAC,GAAG,CAAC,yBAAyB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;gBAE/C,+CAA+C;gBAC/C,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC/B,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,EAAE,gBAAgB,EAAE;oBACpE,OAAO,EAAE,IAAI;oBACb,cAAc,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,yBAAyB;iBACrD,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;oBAChE,IAAI,CAAC,GAAG,CAAC,kCAAkC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;oBACxD,iBAAiB,CAAC,IAAI,CAAC;wBACrB,IAAI,EAAE,MAAM,CAAC,IAAI;wBACjB,IAAI,EAAE;4BACJ,EAAE,EAAE,UAAU,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;yBAC9C;wBACD,QAAQ,EAAE;4BACR,EAAE,EAAE,MAAM,CAAC,EAAE;4BACb,QAAQ,EAAE,OAAO;4BACjB,QAAQ,EAAE,EAAE,EAAE,+BAA+B;yBAC9C;qBACF,CAAC,CAAC;iBACJ;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,GAAG,CAAC,sBAAsB,MAAM,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;aAC1G;SACF;QAED,IAAI,CAAC,GAAG,CAAC,8BAA8B,iBAAiB,CAAC,MAAM,UAAU,CAAC,CAAC;QAC3E,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAErC,gEAAgE;QAChE,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9D,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC7B;QAED,+DAA+D;QAC/D,IAAI,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC5D,IAAI;YACF,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YACvD,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAChC,IAAI,CAAC,GAAG,CAAC,aAAa,iBAAiB,CAAC,MAAM,qBAAqB,CAAC,CAAC;gBACrE,OAAO,iBAAiB,CAAC;aAC1B;SACF;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;SACxC;QAED,IAAI,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QACzD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,OAAoB;QAC/B,IAAI,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QAEtD,IAAI,iBAAiB,GAAG;YACtB,EAAE,EAAE,EAAE;YACN,QAAQ,EAAE,OAAO;YACjB,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,iEAAiE;QACjE,OAAO,CAAC,UAAU,CAAC,6BAA6B,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;YAC/D,IAAI;gBACF,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;gBAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,sCAAsC;gBAEhE,IAAI,CAAC,GAAG,CAAC,uCAAuC,EAAE,eAAe,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;gBAEpG,uDAAuD;gBACvD,IAAI,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;gBACtC,MAAM,EAAE,eAAe,EAAE,GAAG,wDAAa,2BAA2B,GAAC,CAAC;gBAEtE,IAAI,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;gBAClD,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAE3D,IAAI,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;gBACnD,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAEpB,gDAAgD;gBAChD,IAAI,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;gBAErD,2BAA2B;gBAC3B,iBAAiB,GAAG,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;gBAE/C,+CAA+C;gBAC/C,MAAM,QAAQ,GAAG,UAAU,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC;gBACpD,MAAM,UAAU,GAAG;oBACjB,IAAI,EAAE,0BAA0B,EAAE,GAAG;oBACrC,IAAI,EAAE;wBACJ,EAAE,EAAE,QAAQ;qBACb;oBACD,QAAQ,EAAE,iBAAiB;iBAC5B,CAAC;gBAEF,8CAA8C;gBAC9C,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC;gBAChC,IAAI,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;gBAErD,sBAAsB;gBACtB,MAAM,CAAC,mBAAmB,EAAE,CAAC;gBAE7B,OAAO,IAAI,CAAC;aACb;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;gBAC5D,OAAO,KAAK,CAAC;aACd;QACH,CAAC,CAAC,CAAC;QAEH,mFAAmF;QACnF,OAAO,CAAC,UAAU,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;YAC5C,IAAI,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YACxC,OAAO,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC"}